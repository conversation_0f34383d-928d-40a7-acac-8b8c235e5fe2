import * as dayjs from 'dayjs';

export const generateCurlText = (url: string, data: Record<string, any>, method = 'POST', apiKey: string, header: any) => {
    const prettyBody = JSON.stringify(data, null, 2);
    return `
        curl --location '${url}' \\\n${Object.keys(header)
            .map((key) => `--header '${key}: ${header[key]}' \\\n`)
            .join('')}--request ${method} \\\n--data '${prettyBody}'
    `;
};

export const generatePayloadFields = (details: any[]) => {
    const samplePayload: any = {};
    for (const d of details) {
        switch (d.type) {
            case 'string':
                samplePayload[d.mappingField] = 'example';
                break;
            case 'number':
                samplePayload[d.mappingField] = 123;
                break;
            case 'boolean':
                samplePayload[d.mappingField] = true;
                break;
            case 'json':
                samplePayload[d.mappingField] = { sample: 'value' };
                break;
            case 'date':
                samplePayload[d.mappingField] = dayjs().format('YYYY-MM-DD');
                break;
            default:
                samplePayload[d.mappingField] = null;
        }
    }
    return samplePayload;
};
