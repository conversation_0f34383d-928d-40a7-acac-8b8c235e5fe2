export namespace NSMember {
  export enum EStatus {
    INACTIVE = 'INACTIVE',
    ACTIVE = 'ACTIVE',
    LOCKED = 'LOCKED',
    VALIDATED = 'VALIDATED',
    INVALIDATE = 'INVALIDATE',
  }
  export enum EAuthProviderType {
    EMAIL = 'EMAIL',
    GOOGLE = 'GOOGLE',
    FACEBOOK = 'FACEBOOK',
    APPLE = 'APPLE',
  }
  export enum EMemberPackageStatus {
    PENDING = 'PENDING',
    ACTIVE = 'ACTIVE',
    EXPIRED = 'EXPIRED',
    CANCELED = 'CANCELED',
  }
  export enum EMemberPackageHistoryAction {
    UPGRADE = 'UPGRADE',
    DOWNGRADE = 'DOWNGRADE',
  }
}
