import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { REFIX_MODULE } from '../config-module';
import { StripeService } from './web-hook/stripe.service';
import { <PERSON>e<PERSON>ontroller } from './web-hook/stripe.controller';
import { ExternalService } from './external/external.service';
import { ExternalController } from './external/external.controller';
import { ExternalMiddleware } from './external.middleware';
import { RequestMethod } from '@nestjs/common';
import { MemberMiddleware } from '../client/member.middleware';


@ChildModule({
  prefix: REFIX_MODULE.integration,
  providers: [StripeService, ExternalService],
  controllers: [StripeController, ExternalController],
})
export class IntegrationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // consumer
    // .apply(ExternalMiddleware)
    // .forRoutes({
    //   path: `${REFIX_MODULE.integration}/external/*`,
    //   method: RequestMethod.ALL,
    // });

    // consumer
    //   .apply(MemberMiddleware)
    //   .forRoutes({
    //     path: `${REFIX_MODULE.integration}/external/test/*`,
    //     method: RequestMethod.ALL,
    //   });
    }
}
