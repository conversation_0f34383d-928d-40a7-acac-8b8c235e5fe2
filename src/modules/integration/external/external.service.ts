import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { BindRepo, DefTransaction } from "~/@core/decorator";
import {
  ConfigApiRepo,
  ConfigApiDetailRepo,
  MemberApiRepo,
  MemberApiLogRepo,
  MemberPackageRepo,
} from "~/domains/primary";
import { NSConfig, NSMember } from "~/common/enums";
import dayjs from "dayjs";
import { ConfigApiDetailEntity } from "~/domains/primary/config-api/config-api-detail.entity";
import { ENDPOINT } from "~/common/constants/endpoint";
import { RequestExternalDto } from "./dto/external.dto";

@Injectable()
export class ExternalService {
  constructor() { }

  @BindRepo(ConfigApiRepo)
  private configRepo: ConfigApiRepo;

  @BindRepo(ConfigApiDetailRepo)
  private detailRepo: ConfigApiDetailRepo;

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  //#region Dispatch by endpoint
  async dispatchByEndpoint(params: {
    method: string;
    path: string;
    configId: string;
    body: any;
    headers: any;
  }) {
    const { method, path, configId, body, headers } = params;
    const endpoint = ENDPOINT.EXTERNAL_API.find(
      (e) => (e.path ?? '') === (path ?? '') && e.method === method.toUpperCase()
    );

    if (!endpoint) {
      throw new NotFoundException(`Endpoint ${method} ${path}/${configId} not found`);
    }

    // Dispatch tới Handler logic tương ứng
    switch (endpoint.path) {
      case undefined:
        return await this.handleIncomingExternalApi(body, configId);
      case 'test':
        return await this.handleIncomingExternalApi({...body, isTest: true }, configId);
      case 'config/info':
        return await this.handleConfigInfo(body);
      case 'config/log':
        return await this.handleConfigLog(body);
    }

    throw new BadRequestException('Method not allowed');
  }
  //#endregion

  //#region Handler Logic
  @DefTransaction()
  async handleIncomingExternalApi(body: any, configId: string) {
    const config = await this.configRepo.findOne(configId);
    if (!config) throw new NotFoundException("Config package not found");

    const details = await this.detailRepo.find({ where: { configApiId: config.id } });
    if (!details?.length) {
      throw new BadRequestException("Missing config details");
    }

    const memberAPI = await this.memberApiRepo.findOne({ where: { configId } });
    if (!memberAPI) throw new NotFoundException("Member API not found");

    const memberPackage = await this.memberPackageRepo.findOne({
      where: { memberId: config.memberId },
    });
    if (!memberPackage) throw new NotFoundException("Member package not found");

    const isTest = body.isTest || false;
    delete body.isTest;

    if (memberPackage.status !== NSMember.EMemberPackageStatus.ACTIVE) {
      await this.saveApiLog({
        config,
        memberAPI,
        memberPackage,
        body,
        response: { error: "Member package is not active" },
        isTest,
        statusCode: NSConfig.EApiStatusCode.INTERNAL_SERVER_ERROR,
      });

      return {
        statusCode: 500,
        message: "Member package is not active",
        data: null,
      };
    }

    const validationResult = await this.validateOrHandleIncomingData(body, details);

    if (!validationResult.isValid) {
      await this.saveApiLog({
        config,
        memberAPI,
        memberPackage,
        body,
        response: { error: validationResult.message },
        isTest,
        statusCode: NSConfig.EApiStatusCode.BAD_REQUEST,
      });

      return {
        statusCode: validationResult.statusCode,
        message: validationResult.message,
        data: null,
      };
    }

    const mapped = validationResult.data;

    await this.saveApiLog({
      config,
      memberAPI,
      memberPackage,
      body,
      response: { message: "success" },
      isTest,
      statusCode: NSConfig.EApiStatusCode.SUCCESS,
    });

    if (isTest) {
      return {
        statusCode: 200,
        message: "Test OK",
        data: mapped,
      };
    }

    if (memberPackage.currentTransaction >= memberPackage.initialTransactionLimit) {
      throw new BadRequestException("You have reached the maximum number of transactions");
    }

    await this.memberPackageRepo.update(
      { id: memberPackage.id },
      { currentTransaction: memberPackage.currentTransaction + 1 }
    );

    return {
      statusCode: 200,
      message: "OK",
      data: mapped,
    };
  }

  async handleConfigInfo(body: RequestExternalDto) {
    const { configId } = body;
    const config = await this.configRepo.findOne(configId);
    if (!config) throw new NotFoundException("Config package not found");
    const details = await this.detailRepo.find({ where: { configApiId: config.id } });

    const apiInfo = await this.memberApiRepo.findOne({ where: { configId: config.id } });
    return {
      ...config,
      apiInfo,
      fields: details || [],
    };
  }

  async handleConfigLog(body: RequestExternalDto) {
    const { configId } = body;
    const logs = await this.memberApiLogRepo.find({
      where: { configId },
      order: { createdDate: 'DESC' },
    });
    return logs;
  }
  //#endregion

  //#region Helper
  private async validateOrHandleIncomingData(body: any, details: ConfigApiDetailEntity[]) {
    const result: Record<string, any> = {};

    for (const field of details) {
      const value = body[field.mappingField];

      if (field.isRequired && (value === undefined || value === null)) {
        return {
          statusCode: 400,
          isValid: false,
          message: `Missing required field: ${field.mappingField}`,
          data: null,
        };
      }

      const type = field.type.toLowerCase();

      switch (type) {
        case "string":
          if (typeof value !== "string") {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a string`,
              data: null,
            };
          }
          result[field.nameField] = value;
          break;

        case "number":
          if (typeof value !== "number") {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a number`,
              data: null,
            };
          }
          result[field.nameField] = value;
          break;

        case "boolean":
          if (typeof value !== "boolean") {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a boolean`,
              data: null,
            };
          }
          result[field.nameField] = value;
          break;

        case "json":
          try {
            const parsed = typeof value === "object" ? value : JSON.parse(value);
            result[field.nameField] = parsed;
          } catch (e) {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be valid JSON`,
              data: null,
            };
          }
          break;

        case "date":
          if (!dayjs(value).isValid()) {
            return {
              statusCode: 400,
              isValid: false,
              message: `${field.mappingField} must be a valid date`,
              data: null,
            };
          }
          result[field.nameField] = dayjs(value).toISOString();
          break;

        default:
          return {
            statusCode: 400,
            isValid: false,
            message: `Unsupported type: ${field.type}`,
            data: null,
          };
      }
    }

    return {
      statusCode: 200,
      isValid: true,
      message: "Validation successful",
      data: result,
    };
  }

  private async saveApiLog({
    config,
    memberAPI,
    memberPackage,
    body,
    response,
    isTest,
    statusCode,
  }: {
    config: any;
    memberAPI: any;
    memberPackage: any;
    body: any;
    response: any;
    isTest: boolean;
    statusCode: NSConfig.EApiStatusCode;
  }) {
    const log = this.memberApiLogRepo.create({
      configId: config.id,
      memberId: config.memberId,
      memberPackageId: memberPackage.id,
      host: memberAPI.host,
      url: memberAPI.path,
      request: body,
      response,
      isTest,
      method: memberAPI.method,
      statusCode,
    });
    await this.memberApiLogRepo.save(log);
  }
  //#endregion
}
