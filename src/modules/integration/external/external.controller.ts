import { All, Body, NotFoundException, Req } from '@nestjs/common';
import { ExternalService } from './external.service';
import { Request } from 'express';
import { DefController } from '~/@core/decorator';

@DefController('external')
export class ExternalController {
  constructor(private readonly service: ExternalService) { }

  @All('*')
  async handleDynamicRoute(
    @Req() req: Request,
    @Body() body: any
  ) {
    const method = req.method;
    const path = req.path;

    // Lấy phần sau "external/"
    const prefix = '/api/integration/external/';
    const localPath = path.startsWith(prefix) ? path.slice(prefix.length) : path;

    // Tách từng phần path
    const parts = localPath.split('/').filter(Boolean);

    if (parts.length === 0) {
      throw new NotFoundException(`Invalid path: ${path}`);
    }

    const configId = parts[parts.length - 1];
    const routePath = parts.length > 1 ? parts.slice(0, -1).join('/') : undefined;

    return this.service.dispatchByEndpoint({
      method,
      path: routePath,
      configId,
      body,
      headers: req.headers,
    });
  }
}
