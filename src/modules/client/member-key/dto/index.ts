import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";

export class MemberKeyDto {
    @ApiProperty({ description: 'Public key' })
    publicKey: string;

    @ApiProperty({ description: 'API key' })
    apiKey: string;

    @ApiProperty({ description: 'Tên dự án' })
    @IsNotEmpty()
    projectName: string;

    @ApiPropertyOptional({ description: 'Mô tả dự án' })
    @IsOptional()
    projectDescription?: string;

    @ApiPropertyOptional({ description: 'Mã dự án' })
    @IsOptional()
    projectCode: string;
}
