import { DefController, DefPost } from "~/@core/decorator";
import { MemberKeyService } from "./member-key.service";
import { Body } from "@nestjs/common";
import { MemberKeyDto } from "./dto";

@DefController('member-key')
export class MemberKeyController {
    constructor(private readonly service: MemberKeyService) { }

    @DefPost('generate', {
        summary: 'Khai báo key và cập nhật trạng thái member',
    })
    generateKey(@Body() body: MemberKeyDto) {
        return this.service.generateKey(body);
    }
}