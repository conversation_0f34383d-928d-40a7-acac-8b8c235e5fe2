import { Injectable } from "@nestjs/common";
import { BindRepo } from "~/@core/decorator";
import { MemberKeyRepo } from "~/domains/primary";
import { MemberRepo } from "~/domains/primary";
import { NSMember } from "~/common/enums";
import { MemberKeyDto } from "./dto";
import { memberSessionContext } from "../member-session.context";
import { ConfigApiService } from "../config-api/config-api.service";
import { MemberApiRepo } from "~/domains/primary/member-api/member-api.repo";

@Injectable()
export class MemberKeyService {
    constructor(private configApiService: ConfigApiService) { }

    @BindRepo(MemberKeyRepo)
    private memberKeyRepo: MemberKeyRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(MemberApiRepo)
    private memberApiRepo: MemberApiRepo;

    // Khai báo key và cập nhật trạng thái member
    async generateKey(body: MemberKeyDto) {
        const { memberId } = memberSessionContext;
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) throw new Error('Member not found');
        const { publicKey, apiKey } = body;

        await this.memberKeyRepo.save({
            memberId,
            publicKey,
            apiKey,
        });
        await this.memberRepo.update({ id: memberId }, { statusValidate: NSMember.EStatus.VALIDATED });
        return { message: "success" };
    }

    // Update
    async updateKey(body: MemberKeyDto) {
        const { memberId } = memberSessionContext;
        const member = await this.memberRepo.findOne({ where: { id: memberId } });
        if (!member) throw new Error('Member not found');
        const { publicKey, apiKey } = body;

        await this.memberKeyRepo.update({ memberId }, { publicKey, apiKey });

        // Lấy ra toàn bộ member-api để gen lại
        const memberApis = await this.memberApiRepo.find({ where: { memberId } });
        for (const api of memberApis) {
            await this.configApiService.generateMemberAPIConfig(api.configId);
        }

        return { message: "success" };
    }
}
