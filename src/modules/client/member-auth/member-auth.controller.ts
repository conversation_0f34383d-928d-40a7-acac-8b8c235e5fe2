import { Def<PERSON><PERSON><PERSON>er, DefPost, DefGet } from '~/@core/decorator';
import { MemberAuthService } from './member-auth.service';
import { Body, Query, Req, Res } from '@nestjs/common';
import { MemberLoginReq, MemberRegisterReq, MemberUpdateInfoDto } from './dto';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import * as express from 'express';
import { GoogleAuthGuard } from '../@providers/google.guard';
import { JwtAuthGuard } from '../@providers/jwt-auth.guard';
import * as querystring from 'querystring';
import { BusinessException } from '~/@systems/exceptions';
import { memberSessionContext } from '../member-session.context';

@DefController('auth')
export class MemberAuthController {
  constructor(private readonly memberAuthService: MemberAuthService) {}

  @DefPost('login')
  login(@Body() body: MemberLoginReq) {
    return this.memberAuthService.login(body);
  }

  @DefPost('register')
  register(@Body() body: MemberRegisterReq) {
    return this.memberAuthService.register(body);
  }

  /**
   * @vn Lấy thông tin profile user
   * @en Get profile user
   */
  @UseGuards(JwtAuthGuard)
  @DefGet('me', {
    summary: 'Get profile user',
  })
  me(@Req() req: express.Request & { user: { id: string } }) {
    // lấy thông tin user từ DB
    return this.memberAuthService.me(req.user.id);
  }

  // Cập nhật thông tin Member
  @UseGuards(JwtAuthGuard)
  @DefPost('update-info')
  update(@Body() body: MemberUpdateInfoDto) {
    return this.memberAuthService.update(body);
  }

  @UseGuards(GoogleAuthGuard)
  @DefGet('google')
  googleLogin(@Req() req: express.Request, @Res() res: express.Response) {
    const redirectUri = req.query.redirectUri as string;

    if (redirectUri) {
      res.cookie('redirectUri', redirectUri, {
        httpOnly: true,
        maxAge: 5 * 60 * 1000,
        sameSite: 'lax', // hoặc 'none' nếu FE khác domain
        secure: false, // ✅ nếu bạn dùng HTTPS thì set true
      });
    }
  }

  @DefGet('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Req() req, @Res() res: express.Response) {
    const result = await this.memberAuthService.googleLogin(req.user);
    const redirectUri = req.user.redirectUri;
    const query = querystring.stringify(result);
    const finalRedirect = `${redirectUri}?${query}`;
    return res.redirect(finalRedirect);
  }
}
