import { Injectable } from '@nestjs/common';
import { DefTransaction } from '~/@core/decorator';
import { configEnv } from '~/@config/env';
// import * as ethers from 'ethers';
import { Contract, JsonRpcProvider, Wallet, keccak256, toUtf8Bytes } from 'ethers';
import { SyncOnChainMultipleTransactionReq, SyncOnChainTransactionReq } from './dto/chain.dto';
import { BindRepo } from '~/@core/decorator';
import {
  
  MemberApiLogRepo,
  MemberApiRepo,
} from '~/domains/primary';

const { RPC_URL, WALLET_DEPLOYER_PRIVATEKEY, RECORD_HASH_STORAGE_ADDRESS, SCAN_URL } = configEnv();

@Injectable()
export class ChainService {
  constructor() {}

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  private canonicalize(obj: Record<string, any>) {
    console.log(`=====OBJ=====`, obj);
    const sortedKeys = Object.keys(obj).sort();
    const sortedObj: Record<string, any> = {};
    for (const key of sortedKeys) {
      sortedObj[key] = obj[key];
    }
    return JSON.stringify(sortedObj);
  }
  private hashRecord(obj: Record<string, any>) {
    const str = this.canonicalize(obj);
    return keccak256(toUtf8Bytes(str));
  }

  hashRecords(objs: Record<string, any>[]) {
    return objs.map(obj => this.hashRecord(obj));
  }

  async storeHash(body: SyncOnChainTransactionReq) {
    const provider = new JsonRpcProvider(RPC_URL);
    const wallet = new Wallet(WALLET_DEPLOYER_PRIVATEKEY, provider);
    const ct = new Contract(
      RECORD_HASH_STORAGE_ADDRESS,
      ['function storeHash(bytes32 dataHash, string configApiId) external'],
      provider,
    );
    const tx = await (
      await ct
        .connect(wallet)
        ['storeHash(bytes32,string)'](this.hashRecord(body.data), body.configApiId)
    ).wait();
    console.log(`=====TX=====`, tx);
    return {
      txHash: `${SCAN_URL}/tx/${tx.hash}`,
    };
  }

  async storeHashs(body: SyncOnChainMultipleTransactionReq) {
    const provider = new JsonRpcProvider(RPC_URL);
    const wallet = new Wallet(WALLET_DEPLOYER_PRIVATEKEY, provider);
    const dataHashs = this.hashRecords(body.data);
    const ct = new Contract(
      RECORD_HASH_STORAGE_ADDRESS,
      ['function storeHashs(bytes32[] calldata dataHashs, string calldata configApiId) external'],
      provider,
    );
    const tx = await (
      await ct.connect(wallet)['storeHashs(bytes32[],string)'](dataHashs, body.configApiId)
    ).wait();
    console.log(`=====TX=====`, tx);
    return {
      txHash: `${SCAN_URL}/tx/${tx.hash}`,
    };
  }

  @DefTransaction()
  syncOnChainTransaction(body: SyncOnChainTransactionReq) {
    return this.storeHash(body);
  }

  @DefTransaction()
  syncOnChainMultipleTransaction(body: SyncOnChainMultipleTransactionReq) {
    return this.storeHashs(body);
  }
}
