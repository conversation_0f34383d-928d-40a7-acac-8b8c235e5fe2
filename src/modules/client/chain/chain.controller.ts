import { Def<PERSON>ontroller, DefPost } from '~/@core/decorator';
import { ChainService } from './chain.service';
import { Body } from '@nestjs/common';
import { SyncOnChainMultipleTransactionReq, SyncOnChainTransactionReq } from './dto/chain.dto';

@DefController('chain')
export class ChainController {
  constructor(private readonly service: ChainService) {}

  @DefPost('sync-on-chain-transaction')
  syncOnChainTransaction(@Body() body: SyncOnChainTransactionReq) {
    return this.service.syncOnChainTransaction(body);
  }

  @DefPost('sync-on-chain-multiple-transaction')
  syncOnChainMultipleTransaction(@Body() body: SyncOnChainMultipleTransactionReq) {
    return this.service.syncOnChainMultipleTransaction(body);
  }
}
