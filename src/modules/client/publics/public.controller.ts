import { DefController, DefGet } from '~/@core/decorator';
import { PublicService } from './public.service';

@DefController('publics')
export class PublicController {
  constructor(private readonly service: PublicService) {}

  @DefGet('total-report', {
    summary: '<PERSON><PERSON><PERSON> tổng người dùng, tổng cấu hình gói dịch vụ của member, tổng giao dịch',
  })
  totalReport() {
    return this.service.totalReport();
  }
}
