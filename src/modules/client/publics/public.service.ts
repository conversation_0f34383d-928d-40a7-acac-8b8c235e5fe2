import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { NSPayment } from '~/common/enums';
import { ConfigApiRepo , PaymentTransactionRepo } from '~/domains/primary';
import { MemberRepo } from '~/domains/primary';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';

@Injectable()
export class PublicService {
  constructor() { }

  @BindRepo(ConfigApiRepo)
  private configApiRepo: ConfigApiRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  async totalReport() {

    const [totalMember, totalConfigPackage, totalTransaction, totalMemberPackage] = await Promise.all([
      this.memberRepo.count(), // <PERSON>ố lượng thành viên
      this.configApiRepo.count(), // Số lượng cấu hình member đã tạo 
      this.paymentTransactionRepo.count({ where: { status: NSPayment.ETransactionStatus.COMPLETED } }), // Số lượng giao dịch thanh toán
      this.memberPackageRepo.count(), // Số lượng gói dịch vụ member đã mua
    ]);

    return {
      totalMember,
      totalTransaction,
      totalConfigPackage,
      totalMemberPackage,
    };
  }
}
