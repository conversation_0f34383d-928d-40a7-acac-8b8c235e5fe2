import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import {
  ConfigLogDto,
  ConfigApiListDto,
  CreateConfigApiDto,
  UpdateConfigApiDto,
} from './dto/config-package.dto';
import {
  ConfigApiRepo,
  ConfigApiDetailRepo,
} from '~/domains/primary/config-api/config-api.repo';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { NSConfig } from '~/common/enums/config.enum';
import * as dayjs from 'dayjs';
import { MemberApiRepo } from '~/domains/primary/member-api/member-api.repo';
import * as _ from 'lodash';
import { generateCurlText, generatePayloadFields } from '~/common/helpers/generate-text.helper';
import { configEnv } from '~/@config/env';
import { MemberApiLogRepo } from '~/domains/primary/member-api-log/member-api-log.repo';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import { memberSessionContext } from '../member-session.context';
import { BusinessException } from '~/@systems/exceptions';
import { Raw } from 'typeorm';
import { MemberKeyRepo } from '~/domains/primary';
import { MemberRepo } from '~/domains/primary';
import { NSMember } from '~/common/enums';
import { DashboardLogDto } from './dto/dashboard.dto';
import { dateHelper } from '~/common/helpers/date.helper';
import { isValidCodeField } from '~/common/helpers/validate.helper';
import { ConfigApiDetailEntity } from '~/domains/primary/config-api/config-api-detail.entity';
import { SuccessResponse } from '~/@systems/utils';
import { ENDPOINT } from '~/common/constants/endpoint';

@Injectable()
export class ConfigApiService {
  constructor() { }

  @BindRepo(ConfigApiRepo)
  private configRepo: ConfigApiRepo;

  @BindRepo(ConfigApiDetailRepo)
  private detailRepo: ConfigApiDetailRepo;

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(MemberKeyRepo)
  private memberKeyRepo: MemberKeyRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @DefTransaction()
  async create(dto: CreateConfigApiDto) {
    const { memberId } = memberSessionContext;
    const memberPackage = await this.memberPackageRepo.findOne({
      where: {
        memberId: memberId,
      },
    });
    // Check member-key
    const memberKey = await this.memberKeyRepo.findOne({ where: { id: dto.memberKeyId } });
    if (!memberKey) throw new BusinessException('Member key not found');

    if (!memberPackage) throw new BusinessException('Member package not found');
    if (memberPackage.currentConfig >= memberPackage.initialConfigLimit) {
      throw new BusinessException('You have reached the maximum number of configurations');
    }

    const member = await this.memberRepo.findOne({ where: { id: memberId } });
    if (!member) throw new BusinessException('Member not found');
    if (member.statusValidate !== NSMember.EStatus.VALIDATED) {
      throw new BusinessException('Member not validated');
    }

    // Update Member Package
    await this.memberPackageRepo.update(
      {
        id: memberPackage.id,
      },
      {
        currentConfig: memberPackage.currentConfig + 1,
      },
    );

    // Tạo config package
    const config = this.configRepo.create({ ...dto, memberId });
    if (!config.code) config.code = generateCodeHelper.generateCode('CP', 8);
    await this.configRepo.save(config);

    // Validate mappingField
    const fields = dto.fields;
    fields.map((d) => {
      const mappingField = d.mappingField;
      if (!isValidCodeField(mappingField)) {
        throw new BusinessException(`Invalid mapping field: ${mappingField}`);
      }
    });

    const details = fields?.map(detail =>
      this.detailRepo.create({
        ...detail,
        mappingField: detail.mappingField,
        configApiId: config.id,
      }),
    );
    if (details?.length) await this.detailRepo.save(details);

    // Gen API
    const apiGen = await this.generateMemberAPIConfig(config.id);

    return { ...config, fields: details || [], apiInfo: apiGen };
  }

  async findAll() {
    const { memberId } = memberSessionContext;
    return this.configRepo.find({
      where: { memberId, status: NSConfig.EStatus.ACTIVE },
      order: { createdDate: 'DESC' },
    });
  }

  async findPagination(body: ConfigApiListDto) {
    const { memberId } = memberSessionContext;
    const { code, name, status, fromDate, toDate, ...pageRequest } = body;
    const where: any = { memberId };
    if (code) {
      where.code = code;
    }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    if (fromDate && toDate) {
      if (fromDate && toDate) {
        where.createdDate = Raw(alias =>
          `DATE(${alias}) BETWEEN DATE(:from) AND DATE(:to)`,
          { from: fromDate, to: toDate }
        );
      }
    }
    return this.configRepo.findPagination({ where, order: { createdDate: 'DESC' } }, pageRequest);
  }

  async findOne(id: string) {
    const config = await this.configRepo.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    const details = await this.detailRepo.find({ where: { configApiId: config.id } });

    const apiInfo = await this.memberApiRepo.find({ where: { configId: config.id } });
    return {
      ...config,
      apiInfo,
      fields: details || [],
    };
  }

  async findLogs(body: ConfigLogDto) {
    const { configId, ...pageRequest } = body;
    return this.memberApiLogRepo.findPagination(
      { where: { configId: body.configId }, order: { createdDate: 'DESC' } },
      pageRequest,
    );
  }

  @DefTransaction()
  async update(dto: UpdateConfigApiDto) {
    const { id, ...updateDto } = dto;
    const config = await this.findOne(id);
    await this.configRepo.update(
      {
        id,
      },
      {
        name: updateDto.name ?? config.name,
        description: updateDto.description ?? config.description,
      },
    );

    // Xoá toàn bộ detail cũ nếu có cập nhật
    if (dto.details) {
      await this.detailRepo.delete({ configApiId: id });
      const newDetails = dto.details.map(detail =>
        this.detailRepo.create({ ...detail, configApiId: id }),
      );
      await this.detailRepo.save(newDetails);
    }
    // Gen lại API
    const apiInfo = await this.generateMemberAPIConfig(id);
    return { ...config, apiInfo };
  }

  @DefTransaction()
  async inActive(id: string) {
    const config = await this.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    return await this.configRepo.update({ id }, { status: NSConfig.EStatus.INACTIVE });
  }

  @DefTransaction()
  async active(id: string) {
    const config = await this.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    return await this.configRepo.update({ id }, { status: NSConfig.EStatus.ACTIVE });
  }

  //#region Generate API
  @DefTransaction()
  async generateMemberAPIConfig(configId: string) {
    const config = await this.configRepo.findOne(configId);
    if (!config) throw new NotFoundException('Config package not found');

    const details = await this.detailRepo.find({ where: { configApiId: config.id } });
    if (!details?.length) {
      throw new BadRequestException('Cấu hình không có field nào');
    }

    const apiKey = await this.memberKeyRepo.findOne({ where: { memberId: config.memberId } });
    if (!apiKey) throw new NotFoundException('Member key not found');

    const { EXTERNAL_API_HOST } = configEnv();
    const header = {
      'Content-Type': 'application/json',
      'x-api-key': apiKey.apiKey,
    };

    await this.memberApiRepo.delete({ configId });

    const endpoints: Array<{ path?: string; method: NSConfig.EApiMethod }> = ENDPOINT.EXTERNAL_API;

    for (const { path, method } of endpoints) {
      await this.generateAPIDocument({
        memberId: config.memberId,
        configId: config.id,
        details,
        apiKey: apiKey.apiKey,
        host: EXTERNAL_API_HOST,
        header,
        path,
        method,
      });
    }

    return SuccessResponse;
  }

  @DefTransaction()
  async generateAPIDocument(params: {
    memberId: string;
    configId: string;
    details: ConfigApiDetailEntity[];
    apiKey: string;
    host: string;
    header: Record<string, string>;
    path?: string;
    method: NSConfig.EApiMethod;
  }) {
    const { EXTERNAL_API_PATH } = configEnv();
    const {
      memberId,
      configId,
      details,
      apiKey,
      host,
      header,
      path = '',
      method,
    } = params;

    const samplePayload = generatePayloadFields(details);
    const apiPath = `${EXTERNAL_API_PATH}${path ? `/${path}` : ''}/${configId}`;
    const fullUrl = `${host}${apiPath}`;
    const curlText = generateCurlText(fullUrl, samplePayload, method, apiKey, header);

    const apiEntry = this.memberApiRepo.create({
      configId,
      memberId,
      host,
      path,
      method,
      body: samplePayload,
      curlText,
      header: JSON.stringify(header),
    });

    return await this.memberApiRepo.save(apiEntry);
  }


  //#endregion

  //#region Dashboard API Request Log
  async getLogStatsThisWeek(body: DashboardLogDto) {
    const { configId, isTest } = body;
    const { memberId } = memberSessionContext;
    const startOfWeek = dayjs().startOf('week').toDate();
    const endOfWeek = dayjs().endOf('week').toDate();

    const raw = await this.memberApiLogRepo
      .createQueryBuilder('log')
      .select(`EXTRACT(DOW FROM log."createdDate")::int`, 'day')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" = 200)`, 'success')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" != 200)`, 'fail')
      .where(`log."memberId" = :memberId`, { memberId })
      .andWhere(`log."configId" = :configId`, { configId })
      .andWhere(`log."createdDate" BETWEEN :start AND :end`, {
        start: startOfWeek,
        end: endOfWeek,
      })
      .groupBy('day')
      .orderBy('day')
      .getRawMany();

    return this.formatChartData(raw, 7, 'week');
  }

  async getLogStatsThisMonth(body: DashboardLogDto) {
    const { memberId } = memberSessionContext;
    const { configId, isTest } = body;
    const startOfMonth = dayjs().startOf('month').toDate();
    const endOfMonth = dayjs().endOf('month').toDate();

    const raw = await this.memberApiLogRepo
      .createQueryBuilder('log')
      .select(`EXTRACT(DAY FROM log."createdDate")::int`, 'day')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" = 200)`, 'success')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" != 200)`, 'fail')
      .where(`log."memberId" = :memberId`, { memberId })
      .andWhere(`log."configId" = :configId`, { configId })
      .andWhere(`log."createdDate" BETWEEN :start AND :end`, {
        start: startOfMonth,
        end: endOfMonth,
      })
      .groupBy('day')
      .orderBy('day')
      .getRawMany();

    const daysInMonth = dayjs().daysInMonth();
    return this.formatChartData(raw, daysInMonth, 'month');
  }

  async getLogStatsThisYear(body: DashboardLogDto) {
    const { memberId } = memberSessionContext;
    const { configId, isTest } = body;
    const startOfYear = dayjs().startOf('year').toDate();
    const endOfYear = dayjs().endOf('year').toDate();

    const raw = await this.memberApiLogRepo
      .createQueryBuilder('log')
      .select(`EXTRACT(MONTH FROM log."createdDate")::int`, 'month')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" = 200)`, 'success')
      .addSelect(`COUNT(*) FILTER (WHERE "statusCode" != 200)`, 'fail')
      .where(`log."memberId" = :memberId`, { memberId })
      .andWhere(`log."configId" = :configId`, { configId })
      .andWhere(`log."createdDate" BETWEEN :start AND :end`, {
        start: startOfYear,
        end: endOfYear,
      })
      .groupBy('month')
      .orderBy('month')
      .getRawMany();

    return this.formatChartData(raw, 12, 'year');
  }

  async getRequestStatsByConfig() {
    const { memberId } = memberSessionContext;
    const qb = this.memberApiLogRepo.createQueryBuilder('log')
      .select('log.configId', 'configId')
      .addSelect('COUNT(*)', 'count')
      .innerJoin('member_package', 'mp', 'mp.id = log."memberPackageId"')
      .innerJoin('package_plan', 'pkg', 'pkg.id = mp."packagePlanId"')
      .addSelect('pkg.name', 'packageName')
      .where('log.memberId = :memberId', { memberId });

    const raw = await qb
      .groupBy('log.configId')
      .addGroupBy('pkg.name')
      .orderBy('count', 'DESC')
      .getRawMany();

    return {
      labels: raw.map(r => r.packageName),
      datasets: [
        {
          label: 'Request count',
          data: raw.map(r => +r.count),
        },
      ],
    };
  }

  private formatChartData(
    raw: { day?: number; month?: number; success: string; fail: string }[],
    totalSlots: number,
    type: 'week' | 'month' | 'year'
  ) {
    const successData = new Array(totalSlots).fill(0);
    const failData = new Array(totalSlots).fill(0);

    raw.forEach(({ day, month, success, fail }) => {
      const index = (day ?? month) - (type === 'month' ? 1 : 0); // month: 1-12 => index 0-11, year: 1-12 => index 0-11
      successData[index] = +success;
      failData[index] = +fail;
    });

    return {
      totalRequest: raw.reduce((acc, cur) => acc + +cur.success + +cur.fail, 0),
      totalRequestSuccess: raw.reduce((acc, cur) => acc + +cur.success, 0),
      totalRequestFail: raw.reduce((acc, cur) => acc + +cur.fail, 0),
      labels: [...Array(totalSlots)].map((_, i) =>
        type === 'week'
          ? dateHelper.getDayOfWeek(i, 'en')
          : type === 'year'
            ? dateHelper.getMonthOfYear(i, 'en')
            : `${i + 1}`
      ),
      datasets: [
        {
          label: 'Success',
          data: successData,
          borderColor: '#6366f1',
          backgroundColor: 'rgba(70, 241, 57, 0.2)',
        },
        {
          label: 'Failed',
          data: failData,
          borderColor: '#f43f5e',
          backgroundColor: 'rgba(238, 24, 60, 0.2)',
        },
      ],
    };
  }
  //#endregion
}

