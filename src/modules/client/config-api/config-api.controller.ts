import {
    Body,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ConfigApiService } from './config-api.service';
import { ConfigLogDto, ConfigApiListDto, CreateConfigApiDto, UpdateConfigApiDto } from './dto/config-package.dto';
import { DefController, DefPost } from '~/@core/decorator';
import { DashboardLogDto } from './dto/dashboard.dto';

@ApiTags('Config Package API')
@DefController('config-api')
export class ConfigApiController {
    constructor(private readonly service: ConfigApiService) { }

    @DefPost("", {
        summary: 'Tạo mới một cấu hình',
    })
    create(@Body() dto: CreateConfigApiDto) {
        return this.service.create(dto);
    }

    @DefPost('pagination', {
        summary: 'L<PERSON>y danh sách cấu hình',
    })
    findPagination(@Body() body: ConfigApiListDto) {
        return this.service.findPagination(body);
    }

    @DefPost('find-all', {
        summary: 'L<PERSON>y danh sách cấu hình của member',
    })
    findAll() {
        return this.service.findAll();
    }

    @DefPost('logs', {
        summary: 'Lấy danh sách log của cấu hình',
    })
    findLogs(@Body() body: ConfigLogDto) {
        return this.service.findLogs(body);
    }

    @DefPost('detail', {
        summary: 'Lấy thông tin chi tiết cấu hình',
    })
    findOne(@Body('id') id: string) {
        return this.service.findOne(id);
    }

    @DefPost('update', {
        summary: 'Cập nhật cấu hình',
    })
    update(@Body() body: UpdateConfigApiDto) {
        return this.service.update(body);
    }

    @DefPost('inactive', {
        summary: 'Đổi trạng thái cấu hình về Inactive',
    })
    inActive(@Body() body: { id: string }) {
        const { id } = body;
        return this.service.inActive(id);
    }

    @DefPost('active', {
        summary: 'Đổi trạng thái cấu hình về Active',
    })
    active(@Body() body: { id: string }) {
        const { id } = body;
        return this.service.active(id);
    }

    @DefPost('log-stats/this-week', {
        summary: 'Lấy số liệu thống kê của cấu hình trong tuần này',
    })
    getLogStatsThisWeek(@Body() body: DashboardLogDto) {
        return this.service.getLogStatsThisWeek(body);
    }

    @DefPost('log-stats/this-month', {
        summary: 'Lấy số liệu thống kê của cấu hình trong tháng này',
    })
    getLogStatsThisMonth(@Body() body: DashboardLogDto) {
        return this.service.getLogStatsThisMonth(body);
    }

    @DefPost('log-stats/this-year', {
        summary: 'Lấy số liệu thống kê của cấu hình trong năm này',
    })
    getLogStatsThisYear(@Body() body: DashboardLogDto) {
        return this.service.getLogStatsThisYear(body);
    }

    @DefPost('request-stats/all-config', {
        summary: 'Lấy số liệu thống kê của cấu hình theo gói dịch vụ',
    })
    getRequestStatsByConfig() {
        return this.service.getRequestStatsByConfig();
    }
}
