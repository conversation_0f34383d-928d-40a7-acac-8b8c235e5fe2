import { Injectable, NotFoundException } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
  MemberPackageRepo,
  OrderRepo,
  MemberApiLogRepo,
  PackagePlanRepo,
  PaymentTransactionRepo,
  MemberRepo,
  ConfigApiRepo,
} from '~/domains/primary';
import { MemberPackageListDto, MemberPackagePTDto } from './dto/member-package.dto';
import { Between, In, Like } from 'typeorm';
import { memberSessionContext } from '../member-session.context';
import { NSConfig, NSMember } from '~/common/enums';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';

@Injectable()
export class MemberPackageService {
  private stripe: Stripe;
  constructor() {
    this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
  }

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(ConfigApiRepo)
  private configApiRepo: ConfigApiRepo;

  async findOne(id: string) {
    const packageMember = await this.memberPackageRepo.findOne({ where: { id } });
    if (!packageMember) throw new NotFoundException('Member package not found');

    const order = await this.orderRepo.findOne({ where: { id: packageMember.orderId } });
    if (!order) throw new NotFoundException('Order not found');

    const apiLogs = await this.memberApiLogRepo.find({
      where: { memberPackageId: packageMember.id },
    });

    return {
      package: packageMember,
      order,
      log: apiLogs,
    };
  }

  async findPagination(body: MemberPackageListDto) {
    const { memberId } = memberSessionContext;
    const { orderId, packagePlanId, status, expiredDateFrom, expiredDateTo, name, ...pageRequest } =
      body;
    const wheres: any = {};
    if (memberId) {
      wheres.memberId = memberId; // Update lại dùng memberSessionContext
    }
    if (orderId) {
      wheres.orderId = orderId;
    }
    if (packagePlanId) {
      wheres.packagePlanId = packagePlanId;
    }
    if (status) {
      wheres.status = status;
    }

    if (name) {
      const plans = await this.packagePlanRepo.find({ where: { name: Like(`%${name}%`) } });
      wheres.packagePlanId = In(plans.map(p => p.id));
    }

    if (expiredDateFrom && expiredDateTo) {
      wheres.expiredDate = Between(expiredDateFrom, expiredDateTo);
    }

    const { data, total } = await this.memberPackageRepo.findPagination(
      { where: wheres, order: { createdDate: 'DESC' } },
      pageRequest,
    );
    const plans = await this.packagePlanRepo.find();
    const mapping = data.map(item => {
      const plan = plans.find(p => p.id === item.packagePlanId);
      return {
        ...item,
        plan,
      };
    });
    return {
      data: mapping,
      total,
    };
  }

  async findAll() {
    const { memberId } = memberSessionContext;
    return this.memberPackageRepo.find({
      where: { memberId, status: NSMember.EMemberPackageStatus.ACTIVE },
    });
  }

  // Danh sách giao dịch của một member-package
  async findTransaction(body: MemberPackagePTDto) {
    const { memberPackageId, ...pageRequest } = body;
    const packageMember = await this.memberPackageRepo.findOne({ where: { id: memberPackageId } });
    if (!packageMember) throw new NotFoundException('Member package not found');
    return this.paymentTransactionRepo.findPagination(
      { where: { memberPackageId: packageMember.id } },
      pageRequest,
    );
  }

  // Hủy sử dụng gói
  @DefTransaction()
  async cancelMemberPackage(id: string) {
    const memberPackage = await this.memberPackageRepo.findOne({ where: { id } });
    if (!memberPackage) throw new NotFoundException('Member package not found');

    // Inactive toàn bộ cấu hình
    await this.configApiRepo.update(
      { memberId: memberPackage.memberId },
      { status: NSConfig.EStatus.INACTIVE },
    );

    // Hủy tự động gia hạn
    await this.cancelAutoRenewal(id);

    return await this.memberPackageRepo.update(
      { id },
      { status: NSMember.EMemberPackageStatus.CANCELED },
    );
  }

  // Hủy tự động gia hạn gói bằng subscriptionId trên Stripe
  @DefTransaction()
  async cancelAutoRenewal(id: string) {
    const memberPackage = await this.memberPackageRepo.findOne({ where: { id } });

    // Kiểm tra member package có tồn tại không
    if (!memberPackage) throw new NotFoundException('Member package not found');

    // Kiểm tra trạng thái hiện tại
    if (memberPackage.status === NSMember.EMemberPackageStatus.CANCELED) {
      return { message: 'Member package already canceled' };
    }

    // Kiểm tra trạng thái đã hết hạn
    if (memberPackage.status === NSMember.EMemberPackageStatus.EXPIRED) {
      return { message: 'Member package already expired' };
    }

    // Lấy subscriptionId trên Stripe
    const { subscriptionId } = memberPackage;

    // Hủy subscription trên Stripe nếu có
    if (subscriptionId) {
      await this.stripe.subscriptions.cancel(subscriptionId);
    }

    // Cập nhật database một lần duy nhất
    await this.memberPackageRepo.update(
      { id },
      {
        status: NSMember.EMemberPackageStatus.CANCELED,
        isAutoRenewal: false,
      },
    );

    return { message: 'Huỷ gói thành công' };
  }
}
