import { Injectable } from "@nestjs/common";
import { BindRepo, DefTransaction } from "~/@core/decorator";
import { PaymentTransactionRepo } from "~/domains/primary/payment-transaction/payment-transaction.repo";
import { CreatePaymentTransactionDto, InvoiceDetailDto, PaymentSubscriptionDto, PaymentTransactionListDto } from "./dto/payment-transaction.dto";
import Stripe from 'stripe';
import { configEnv } from "~/@config/env";
import { NSPayment } from "~/common/enums/payment.enum";
import { OrderRepo } from "~/domains/primary/order/order.repo";
import { NotFoundException } from "@nestjs/common";
import { generateCodeHelper } from "~/common/helpers/generate-code.helper";
import { OrderItemRepo, PackagePlanRepo } from "~/domains/primary/";
import { MemberRepo } from "~/domains/primary/member/member.repo";
import { MemberPackageRepo } from "~/domains/primary/member-package/member-package.repo";
import { memberSessionContext } from "../member-session.context";
import { NSMember, NSOrder, NSPackage } from "~/common/enums";
import { BusinessException } from "~/@systems/exceptions";
import * as dayjs from 'dayjs';
import { parse } from "path";

@Injectable()
export class PaymentTransactionService {
    private stripe: Stripe
    constructor(
    ) { this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {}); }

    @BindRepo(PaymentTransactionRepo)
    private paymentTransactionRepo: PaymentTransactionRepo;

    @BindRepo(OrderRepo)
    private orderRepo: OrderRepo;

    @BindRepo(OrderItemRepo)
    private orderItemsRepo: OrderItemRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @DefTransaction()
    async create(dto: CreatePaymentTransactionDto) {
        const { memberId } = memberSessionContext;
        const { orderId, paymentProvider, isAutoRenewal, amount, amountVat, unit, timeRegister } = dto;
        const [order, planPackage, member] = await Promise.all([
            this.orderRepo.findOne({ where: { id: orderId } }),
            this.orderItemsRepo.findOne({ where: { orderId } }),
            this.memberRepo.findOne({ where: { id: memberId } }),
        ]);

        if (!order) throw new NotFoundException('Order not found');
        if (!planPackage) throw new NotFoundException('Plan package not found');
        if (!member) throw new NotFoundException('Member not found');

        // Kiểm timeRegister và unit là Yearly chỉ được 2 năm
        if (unit === NSPackage.EPlanTypePayment.YEARLY 
            && timeRegister > 2 
            && paymentProvider === NSPayment.EPaymentProvider.STRIPE
        ) {
            throw new BusinessException('Only 2 years allowed for Stripe');
        }

        const { REDIRECT_URL_PAYMENT } = configEnv();

        const plan = await this.packagePlanRepo.findOne({ where: { id: planPackage.packagePlanId } });
        if (!plan) throw new NotFoundException('Plan not found');

        let newMemberPackage = null;
        const memberPackage = this.memberPackageRepo.create({
            memberId,
            orderId,
            packagePlanId: plan.id,
            initialTransactionLimit: plan.transactionLimit,
            currentTransaction: 0,
            initialConfigLimit: plan.configLimit,
            currentConfig: 0,
            status: 'PENDING',
        });
        newMemberPackage = await this.memberPackageRepo.save(memberPackage);

        if (plan.isTrial) {
            // Update member package
            await this.memberPackageRepo.update({ id: newMemberPackage.id }, {
                status: NSMember.EMemberPackageStatus.ACTIVE,
                activatedDate: new Date().toISOString(),
                expiredDate: dayjs().add(1, 'month').format('YYYY-MM-DD'),
            });
            // Update order
            await this.orderRepo.update({ id: orderId }, {
                status: NSOrder.EStatus.COMPLETED,
                paymentStatus: NSOrder.EPaymentStatus.PAID,
                paymentDate: new Date().toISOString(),
            });
            return {
                checkoutUrl: `${REDIRECT_URL_PAYMENT}/payment-success`,
                sessionId: '',
            };
        }

        const count = await this.paymentTransactionRepo.count();
        // Cập nhật lại giá trị đơn hàng và vat
        const vat = +amountVat - +amount;

        const formatAmountVat = Number((amountVat * 100).toFixed(0));

        // 2. Tạo payment transaction
        const transaction = this.paymentTransactionRepo.create({
            ...dto,
            memberId,
            amount: formatAmountVat / 100,
            grossAmount: formatAmountVat / 100,
            code: generateCodeHelper.generateSOCode(count, 'PT'),
            memberPackageId: newMemberPackage.id,
        });
        console.log('transaction', transaction);
        const savedTransaction = await this.paymentTransactionRepo.save(transaction);
        const transactionId = savedTransaction.id;

        // Tính lại giá trị đơn hàng
        await this.orderRepo.update({ id: orderId }, { 
            status: NSOrder.EStatus.PENDING, 
            totalPrice: amount, 
            totalPriceVat: formatAmountVat / 100,
            vat 
        });

        // 4. Nếu Stripe thì tạo checkout session
        if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
            // Tạo checkout session
            const checkoutSession = await this.createStripeCheckoutSession({
                orderId,
                transactionId,
                customerEmail: member.email,
                planId: planPackage.packagePlanId,
                paymentType: unit,
                memberPackageId: newMemberPackage.id,
                isAutoRenewal,
                amount: formatAmountVat,
                timeRegister,
            });

            // Cập nhật lại transaction với sessionId
            if (checkoutSession.sessionId) {
                await this.paymentTransactionRepo.update(transactionId, {
                    sessionRefId: checkoutSession.sessionId,
                });
            }

            return {
                ...savedTransaction,
                memberPackage: newMemberPackage,
                checkoutUrl: checkoutSession?.checkoutUrl,
                sessionId: checkoutSession?.sessionId,
            };
        }
    }

    async findAll() {
        const { memberId } = memberSessionContext;
        return this.paymentTransactionRepo.find({ where: { memberId } });
    }

    async findOne(id: string) {
        return this.paymentTransactionRepo.findOne(id);
    }

    async findPagination(body: PaymentTransactionListDto) {
        const { code, memberId, orderId, status, ...pageRequest } = body;
        const where: any = {}
        if (code) {
            where.code = code;
        }
        if (memberId) {
            where.memberId = memberId; // Update lại dùng memberContextSession 
        }
        if (orderId) {

            where.orderId = orderId;
        }
        if (status) {
            where.status = status;
        }
        return this.paymentTransactionRepo.findPagination({ where, order: { createdDate: 'DESC' } }, pageRequest);
    }


    //#region STRIPE
    /**
     * Tạo Stripe Checkout Session cho cả payment và subscription
     */
    @DefTransaction()
    async createStripeCheckoutSession(body: PaymentSubscriptionDto) {
        const {
            orderId,
            transactionId,
            customerEmail,
            planId,
            paymentType,
            memberPackageId,
            isAutoRenewal,
            amount,
            timeRegister,
        } = body;

        const { REDIRECT_URL_PAYMENT } = configEnv();

        // Lấy thông tin plan
        const plan = await this.packagePlanRepo.findOne({ where: { id: planId } });
        if (!plan) throw new NotFoundException('Plan not found');

        const priceId = paymentType === NSPackage.EPlanTypePayment.MONTHLY
            ? plan.stripePriceMonthId
            : plan.stripePriceYearId;

        if (!priceId) throw new BusinessException('Price not found');

        // Metadata gửi lên Stripe
        const metaData = {
            orderId,
            transactionId,
            memberPackageId,
            planId,
            paymentType,
            timeRegister: String(timeRegister || 1),
        };

        const baseSessionConfig = {
            customer_email: customerEmail,
            success_url: `${REDIRECT_URL_PAYMENT}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: `${REDIRECT_URL_PAYMENT}`,
            metadata: metaData,
        };

        // Kiểm tra hoặc tạo Stripe customer
        let customer: Stripe.Customer;
        const customers = await this.stripe.customers.list({ email: customerEmail, limit: 1 });
        customer = customers.data[0] || await this.stripe.customers.create({
            email: customerEmail,
            metadata: { memberPackageId, orderId },
        });

        // 1. Checkout thanh toán 1 lần
        const session = await this.stripe.checkout.sessions.create({
            mode: 'payment',
            line_items: [{
                price_data: {
                    currency: 'usd',
                    product_data: { name: `${plan.name} gói ${timeRegister} ${paymentType === 'MONTHLY' ? 'tháng' : 'năm'}` },
                    unit_amount: amount,
                },
                quantity: 1,
            }],
            invoice_creation: { enabled: true },
            ...baseSessionConfig
        });

        // 2. Tạo subscription tự động gia hạn (nếu bật)
        if (isAutoRenewal) {
            const start = dayjs(); // Ngày bắt đầu
            const nextBillingDate = start
                .add(timeRegister, paymentType === NSPackage.EPlanTypePayment.MONTHLY ? 'month' : 'year');
            const trialPeriodDay = Math.max(0, Math.min(nextBillingDate.diff(start, 'day'), 730)); // Stripe tối đa 2 năm

            const subscription = await this.stripe.subscriptions.create({
                customer: customer.id,
                items: [{ price: priceId }],
                metadata: metaData,
                trial_period_days: trialPeriodDay,
                proration_behavior: 'none'
            });

            await this.memberPackageRepo.update({ id: memberPackageId }, {
                subscriptionId: subscription.id,
                subscriptionProvider: NSPayment.EPaymentProvider.STRIPE,
                isAutoRenewal: true,
                nextPaymentDate: nextBillingDate.toDate(),
            });
        }

        // 3. Update transaction
        await this.paymentTransactionRepo.update(transactionId, {
            sessionRefId: session.id,
        });

        return {
            checkoutUrl: session.url,
            sessionId: session.id,
        };
    }

    /**
     * 
     * @param invoiceId 
     * @returns 
     */
    async getInvoiceDetails(body: InvoiceDetailDto) {
        const { invoiceId } = body;
        try {
            // Kiểm tra invoiceId đang có trong order nào không
            const order = await this.orderRepo.findOne({ where: { invoiceId } });
            if (!order) throw new NotFoundException('Order not found');

            const invoice = await this.stripe.invoices.retrieve(invoiceId);

            return {
                id: invoice.id,
                status: invoice.status,
                amount_due: invoice.amount_due,
                amount_paid: invoice.amount_paid,
                customer_email: invoice.customer_email,
                hosted_invoice_url: invoice.hosted_invoice_url,
                invoice_pdf: invoice.invoice_pdf, // 🔗 Link tải PDF
                created: invoice.created,
                due_date: invoice.due_date,
            };
        } catch (error) {
            console.error(`Error fetching invoice: ${error.message}`);
            throw new NotFoundException(`Invoice ${invoiceId} not found`);
        }
    }
    //#endregion
}
