import { Injectable, NotFoundException } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { OrderRepo } from '~/domains/primary';
import { OrderListDto } from '~/modules/client/order/dto/order.dto';
import { Between, In } from 'typeorm';
import { MemberRepo } from '~/domains/primary';
import { OrderItemRepo } from '~/domains/primary';
import { PackagePlanRepo } from '~/domains/primary';

@Injectable()
export class OrderService {
  constructor() {}

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(OrderItemRepo)
  private orderItemRepo: OrderItemRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  async findPagination(body: OrderListDto) {
    const {
      code,
      memberId,
      status,
      createdDateFrom,
      createdDateTo,
      paymentStatus,
      paymentDateFrom,
      paymentDateTo,
      packagePlanId,
      ...pageRequest
    } = body;
    const where: any = {};
    if (code) {
      where.code = code;
    }
    if (memberId) {
      where.memberId = memberId;
    }
    if (status) {
      where.status = status;
    }
    if (createdDateFrom && createdDateTo) {
      where.createdDate = Between(createdDateFrom, createdDateTo);
    }
    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }
    if (paymentDateFrom && paymentDateTo) {
      where.paymentDate = Between(paymentDateFrom, paymentDateTo);
    }
    if (packagePlanId) {
      where.packagePlanId = packagePlanId;
    }
    const { data, total } = await this.orderRepo.findPagination(
      { where, order: { createdDate: 'DESC' } },
      pageRequest,
    );
    const members = await this.memberRepo.find();
    const mapping = data.map(item => {
      const member = members.find(m => m.id === item.memberId);
      return {
        ...item,
        member,
      };
    });

    return {
      data: mapping,
      total,
    };
  }

  async findOne(id: string) {
    const order = await this.orderRepo.findOne(id);
    if (!order) throw new NotFoundException('Order not found');
    const items = await this.orderItemRepo.find({ where: { orderId: order.id } });
    const packages = await this.packagePlanRepo.find({
      where: { id: In(items.map(item => item.packagePlanId)) },
    });
    return { ...order, items, packages };
  }
}
