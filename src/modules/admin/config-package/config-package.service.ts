import { Injectable, NotFoundException } from '@nestjs/common';
import {
  ConfigLogDto,
  ConfigApiListDto
} from './dto/config-package.dto';
import {
  ConfigApiRepo,
  ConfigApiDetailRepo,
} from '~/domains/primary/config-api/config-api.repo';
import { BindRepo } from '~/@core/decorator';
import { NSConfig } from '~/common/enums/config.enum';
import { MemberApiRepo } from '~/domains/primary/member-api/member-api.repo';
import * as _ from 'lodash';
import { MemberApiLogRepo } from '~/domains/primary/member-api-log/member-api-log.repo';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { memberSessionContext } from '~/modules/client/member-session.context';

@Injectable()
export class ConfigApiService {
  constructor() {}

  @BindRepo(ConfigApiRepo)
  private configRepo: ConfigApiRepo;

  @BindRepo(ConfigApiDetailRepo)
  private detailRepo: ConfigApiDetailRepo;

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  async findPagination(body: ConfigApiListDto) {
    const { memberId } = memberSessionContext;
    const { code, name, status, ...pageRequest } = body;
    const where: any = { memberId };
    if (code) {
      where.code = code;
    }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    return this.configRepo.findPagination({ where }, pageRequest);
  }

  async findOne(id: string) {
    const config = await this.configRepo.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    const details = await this.detailRepo.find({ where: { configApiId: config.id } });

    const apiInfo = await this.memberApiRepo.findOne({ where: { configId: config.id } });
    return {
      ...config,
      apiInfo,
      fields: details || [],
    };
  }

  async findLogs(body: ConfigLogDto) {
    const { configId, ...pageRequest } = body;
    return this.memberApiLogRepo.findPagination(
      { where: { configId: body.configId } },
      pageRequest,
    );
  }
}
