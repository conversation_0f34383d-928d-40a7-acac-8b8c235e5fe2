import { Injectable, NotFoundException } from '@nestjs/common';
import { AdvisoryRepo } from '~/domains/primary/advisory/advisory.repo';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { NSAdvisory } from '~/common/enums/advisory.enum';
import { AdvisoryListDto, CreateAdvisoryDto } from './dto/advisory.dto';
import { Between, ILike } from 'typeorm';

@Injectable()
export class AdvisoryService {
  constructor() {}

  @BindRepo(AdvisoryRepo)
  private advisoryRepo: AdvisoryRepo;

  @DefTransaction()
  async create(dto: CreateAdvisoryDto) {
    const advisory = this.advisoryRepo.create(dto);
    return await this.advisoryRepo.save(advisory);
  }

  async findAll(params: AdvisoryListDto) {
    const {
      fullName,
      createdDateFrom,
      createdDateTo,
      email,
      phone,
      subject,
      status,
      customerId,
      ...pageRequest
    } = params;
    const where: any = {};
    if (fullName) {
      where.fullName = ILike(`%${fullName}%`);
    }
    if (email) {
      where.email = ILike(`%${email}%`);
    }
    if (phone) {
      where.phone = phone;
    }
    if (subject) {
      where.subject = subject;
    }
    if (status) {
      where.status = status;
    }
    if (customerId) {
      where.customerId = customerId;
    }
    if (createdDateFrom && createdDateTo) {
      where.createdDate = Between(createdDateFrom, createdDateTo);
    }
    return await this.advisoryRepo.findPagination({ where }, pageRequest);
  }

  async findOne(id: string) {
    const item = await this.advisoryRepo.findOne({ where: { id } });
    if (!item) throw new NotFoundException('Advisory not found');
    return item;
  }

  // Update status
  @DefTransaction()
  async updateStatus(body: { id: string; status: NSAdvisory.EStatus }) {
    const item = await this.findOne(body.id);
    return await this.advisoryRepo.update({ id: body.id }, { status: body.status });
  }
}
