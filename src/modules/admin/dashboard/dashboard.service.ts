import { Injectable } from "@nestjs/common";
import { BindRepo } from "~/@core/decorator";
import { OrderRepo } from "~/domains/primary/order/order.repo";
import { OrderItemRepo } from "~/domains/primary/order-item/order-item.repo";
import { PackagePlanRepo } from "~/domains/primary/package/package.repo";
import { In, Raw } from "typeorm";
import * as dayjs from "dayjs";
import { PaymentTransactionRepo } from "~/domains/primary/payment-transaction/payment-transaction.repo";
import { NSPayment } from "~/common/enums";

@Injectable()
export class DashboardService {
    constructor() { }

    @BindRepo(OrderRepo)
    private orderRepo: OrderRepo;

    @BindRepo(OrderItemRepo)
    private orderItemRepo: OrderItemRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    @BindRepo(PaymentTransactionRepo)
    private paymentTransactionRepo: PaymentTransactionRepo;

    // Thống kê gói dịch vụ trong 1 năm ( tháng 1 -> tháng 12 )
    async getPackageStatisticsChart(year: number = dayjs().year()) {
        const orders = await this.orderRepo.find({
            where: {
                paymentStatus: 'PAID',
                paymentDate: Raw(alias => `${alias} >= :start AND ${alias} <= :end`, {
                    start: `${year}-01-01T00:00:00Z`,
                    end: `${year}-12-31T23:59:59Z`,
                }),
            },
            select: ['id', 'paymentDate'],
        });

        if (!orders.length) {
            return this.generateEmptyChart();
        }

        const orderIds = orders.map(o => o.id);

        const orderItems = await this.orderItemRepo.find({
            where: { orderId: In(orderIds) },
            select: ['orderId', 'packagePlanId', 'quantity'],
        });

        const orderIdToMonth = new Map(
            orders.map(o => [o.id, dayjs(o.paymentDate).month()]) // 0-11
        );

        const monthlyCountMap = new Map<number, Map<string, number>>();
        for (let i = 0; i < 12; i++) monthlyCountMap.set(i, new Map());

        const totalCountMap = new Map<string, number>(); // dùng cho pie chart

        for (const item of orderItems) {
            const month = orderIdToMonth.get(item.orderId);
            if (month === undefined) continue;

            // tháng
            const monthMap = monthlyCountMap.get(month)!;
            monthMap.set(item.packagePlanId, (monthMap.get(item.packagePlanId) || 0) + item.quantity);

            // năm
            totalCountMap.set(item.packagePlanId, (totalCountMap.get(item.packagePlanId) || 0) + item.quantity);
        }

        const packageIds = Array.from(new Set(orderItems.map(i => i.packagePlanId)));
        const packages = await this.packagePlanRepo.findByIds(packageIds);
        const packageMap = new Map(packages.map(p => [p.id, p.name]));

        // ✅ Dữ liệu biểu đồ cột
        const monthLabels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
        const packageIdToLabel = new Map<string, string>();
        for (const pkg of packages) {
            packageIdToLabel.set(pkg.id, pkg.name);
        }

        const colorPalette = this.generateColorPalette(packages.length);
        const pkgToColor = new Map<string, string>();
        packages.forEach((p, i) => pkgToColor.set(p.id, colorPalette[i]));

        const datasetMap = new Map<string, number[]>(); // packagePlanId => [12 tháng]
        for (const pkgId of packageIds) {
            datasetMap.set(pkgId, Array(12).fill(0));
        }

        for (let month = 0; month < 12; month++) {
            const monthMap = monthlyCountMap.get(month)!;
            for (const [pkgId, count] of monthMap.entries()) {
                datasetMap.get(pkgId)![month] = count;
            }
        }

        const barChart = {
            labels: monthLabels,
            datasets: Array.from(datasetMap.entries()).map(([pkgId, data]) => ({
                label: packageMap.get(pkgId) || 'Unknown',
                data,
                backgroundColor: pkgToColor.get(pkgId),
            })),
        };

        // ✅ Dữ liệu biểu đồ tròn (pie), phần trăm
        const sumTotal = Array.from(totalCountMap.values()).reduce((acc, cur) => acc + cur, 0);
        const percentage = Array.from(totalCountMap.values()).map(count => parseFloat(((count / sumTotal) * 100).toFixed(2)));
        const pieChart = {
            labels: Array.from(totalCountMap.keys()).map(pkgId => packageMap.get(pkgId) || 'Unknown'),
            datasets: [
                {
                    label: 'Số lượng gói dịch vụ (%)',
                    data: percentage,
                    backgroundColor: Array.from(totalCountMap.keys()).map(pkgId => pkgToColor.get(pkgId)!),
                },
            ],
        }

        return { barChart, pieChart };
    }

    // Thống kê phương thức thanh toán /giao dịch trong 1 năm ( tháng 1 -> tháng 12 )
    async getPaymentGatewayStats(year: number = dayjs().year()) {
        const start = dayjs(`${year}-01-01`).startOf('month').toDate();
        const end = dayjs(`${year}-12-31`).endOf('month').toDate();

        const transactions = await this.paymentTransactionRepo.find({
            where: {
                status: NSPayment.ETransactionStatus.COMPLETED,
                transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
            },
            select: ['id', 'paymentProvider', 'transactionDate'],
        });

        const methodMap = new Map<string, number[]>();

        for (const tx of transactions) {
            const method = tx.paymentProvider || 'unknown';
            const month = dayjs(tx.transactionDate).month(); // 0-based (0 = Jan)

            if (!methodMap.has(method)) {
                methodMap.set(method, Array(12).fill(0));
            }
            methodMap.get(method)![month] += 1;
        }

        const labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
        const colorPalette = this.generateColorPalette(methodMap.size);

        const datasets = Array.from(methodMap.entries()).map(([method, data], index) => ({
            label: method,
            data,
            backgroundColor: colorPalette[index],
        }));

        return { labels, datasets };
    }

    // Thống kế doanh thu trong 1 năm ( tháng 1 -> tháng 12 )
    async getRevenueStats(year: number = dayjs().year()) {
        const start = dayjs(`${year}-01-01`).startOf('month').toDate();
        const end = dayjs(`${year}-12-31`).endOf('month').toDate();

        const transactions = await this.paymentTransactionRepo.find({
            where: {
                status: NSPayment.ETransactionStatus.COMPLETED,
                transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
            },
            select: ['id', 'amount', 'transactionDate'],
        });

        const monthMap = new Map<number, number>();
        for (let i = 0; i < 12; i++) monthMap.set(i, 0);

        for (const tx of transactions) {
            const month = dayjs(tx.transactionDate).month(); // 0-based (0 = Jan)
            monthMap.set(month, monthMap.get(month)! + +tx.amount);
        }

        const labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
        const data = Array.from(monthMap.values());

        return { labels, datasets: [{ label: 'Doanh thu $', data }] };
    }

    private generateEmptyChart() {
        const emptyLabels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
        return {
            barChart: {
                labels: emptyLabels,
                datasets: [],
            },
            pieChart: {
                labels: [],
                data: [],
                percentage: [],
                backgroundColor: [],
            },
        };
    }

    private generateColorPalette(count: number): string[] {
        const baseColors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#00A36C', '#D2691E', '#8A2BE2', '#FF4500',
            '#6A5ACD', '#3CB371', '#B22222', '#2E8B57', '#FFD700',
        ];
        const result: string[] = [];
        for (let i = 0; i < count; i++) {
            result.push(baseColors[i % baseColors.length]);
        }
        return result;
    }
}
