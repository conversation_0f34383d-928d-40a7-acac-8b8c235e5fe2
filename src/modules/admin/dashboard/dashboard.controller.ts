import { DefController, DefGet } from "~/@core/decorator";
import { DashboardService } from "./dashboard.service";
import { Query } from "@nestjs/common";


@DefController('dashboard')
export class DashboardController {
    constructor(
        private readonly service: DashboardService
    ) {}

    @DefGet('package-statistics', {
        summary: 'Lấy dữ liệu biểu đồ thống kê gói dịch vụ',
    })
    getPackageStatisticsChart(@Query('year') year: number) {
        return this.service.getPackageStatisticsChart(year);
    }

    @DefGet('payment-gateway-stats', {
        summary: 'Lấy dữ liệu biểu đồ thống kê phương thức thanh toán',
    })
    getPaymentGatewayStats(@Query('year') year: number) {
        return this.service.getPaymentGatewayStats(year);
    }

    @DefGet('revenue-stats', {
        summary: 'Lấy dữ liệu biểu đồ thống kê doanh thu',
    })
    getRevenueStats(@Query('year') year: number) {
        return this.service.getRevenueStats(year);
    }
}