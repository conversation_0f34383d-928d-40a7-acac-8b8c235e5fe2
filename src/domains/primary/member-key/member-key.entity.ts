import { Entity, Column, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('member_key')
export class MemberKeyEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'ID của thành viên' })
  @Column({ type: 'uuid' })
  @Index()
  memberId: string;

  @ApiProperty({ description: 'Public key' })
  @Column({ type: 'text', nullable: true })
  publicKey: string;

  @ApiProperty({ description: 'API key' })
  @Column({ type: 'text', nullable: true })
  apiKey: string;

  @ApiProperty({ description: 'Tên dự án' })
  @Column({ type: 'varchar', length: 255 })
  projectName: string;

  @ApiProperty({ description: 'Mô tả dự án', required: false })
  @Column({ type: 'varchar', length: 255, nullable: true })
  projectDescription?: string;

  @ApiProperty({ description: 'Mã dự án' })
  @Column({ type: 'varchar', length: 255, nullable: true })
  projectCode: string;
}
