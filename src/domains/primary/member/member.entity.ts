import { Entity, Column } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSMember } from '~/common/enums';

@Entity('member')
export class MemberEntity extends PrimaryBaseEntity {
  @ApiProperty({ example: '<EMAIL>' })
  @Column({ nullable: true, unique: true, type: 'varchar', length: 255 })
  email: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'varchar', length: 255 })
  password?: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'text' })
  fullName?: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'text' })
  avatar?: string;

  @ApiProperty()
  @Column({ type: 'varchar', default: NSMember.EStatus.ACTIVE })
  status: NSMember.EStatus;

  @ApiProperty()
  @Column({ type: 'varchar', default: NSMember.EStatus.INVALIDATE })
  statusValidate: NSMember.EStatus;

  // Đã sử dụng gói dùng thử
  @Column({ type: 'boolean', default: false })
  isTrialUsed: boolean;
}
