import { Entity, Column } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSMember } from '~/common/enums';

@Entity('chain_transaction')
export class ChainTransactionEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Member ID' })
  @Column('uuid')
  memberId: string;

  @ApiProperty({ description: 'Config code' })
  @Column('varchar', { length: 255 })
  configCode: string;

  @ApiProperty({ description: 'Owner address' })
  @Column('varchar', { length: 255 })
  ownerAddress: string;

  @ApiProperty({ description: 'Transaction fee' })
  @Column('decimal', { precision: 10, scale: 2 })
  transactionFee: number; // phí giao dịch

  @ApiProperty({ description: 'Transaction hash' })
  @Column('varchar', { length: 255 })
  txHash: string;

  @ApiProperty({ description: 'Transaction status' })
  @Column('varchar', { length: 255 })
  status: string;

  @ApiProperty({ description: 'Data on chain' })
  @Column({ type: 'text' })
  dataOnChain: string;

  @ApiProperty({ description: 'Data of chain' })
  @Column({ type: 'jsonb' })
  dataOfChain: any;
}
